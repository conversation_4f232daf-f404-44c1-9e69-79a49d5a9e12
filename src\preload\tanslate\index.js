import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { MyIdb } from './indexeddb'
export class Translater {
  config
  dependPlatform
  isNotMoney = false
  db

  init(dependPlatform) {
    this.db = new MyIdb()
    this.dependPlatform = dependPlatform
    this.setTranslateConfig()
    ipcRenderer.on('updateViewTranslateConfig', (event, arg) => {
      this.setTranslateConfig(arg)
    })
  }

  async setTranslateConfig(config) {
    if (config) {
      this.config = config
      if (this.dependPlatform) {
        this.dependPlatform.translateList()
      }
      return
    }
    if (this.dependPlatform) {
      const r = await ipcRenderer.invoke('getTranslateConfig', this.dependPlatform.platform)
      if (r) {
        this.config = r
      }
    } else {
      throw new Error('dependPlatform is null plase set....')
    }
  }

  async _translate(params) {
    if (!params.text) {
      return
    }
    if (!this.config.self) {
      return
    }
    let result = await this.db.getTranslate(params)
    console.log('params\n%o\nresult:%o', params, result)

    if (!result) {
      result = await ipcRenderer.invoke('getMessageTranslate', JSON.stringify(params))
      if (result.code === 1 && result.data.result !== params.text) {
        if (params.isInput) {
          this.db.addTranslate({
            text: params.text,
            toLang: params.toLang,
            recive_lang: this.config.recive_lang,
            transText: result.data.result
          })
        } else {
          this.db.addTranslate({
            text: params.text,
            toLang: params.toLang,
            transText: result.data.result
          })
        }
        this.isNotMoney && (this.isNotMoney = false)
      } else if (result.code === 400010) {
        if (this.isNotMoney === false) {
          alert(`${result.msg}......`)
          this.isNotMoney = true
        }
      }
    }
    const tv = this.formatResponse(result) || ''
    if (tv && tv === params.text) {
      return
    } else if (params.isInput && tv === '') {
      return params.text
    }
    return tv
  }

  async translateInput(v) {
    if (!(this.config && this.config.trans_over)) {
      return v
    }
    let params = {
      text: v,
      engineCode: this.config.engineCode,
      fromLang: 'auto',
      toLang: this.config.send_lang,
      recive_lang: this.config.recive_lang,
      time: 0, // 接收时间
      type: 'out', // 接收a
      ccountId: this.dependPlatform.userId,
      fansId: this.dependPlatform.chatUserId,
      platform: this.dependPlatform.platform
    }

    return await this._translate({ isInput: true, ...params })
  }

  createLoadingDiv(v) {
    var loadingDiv = document.createElement('div')
    loadingDiv.style.display = 'flex'
    loadingDiv.style.flexDirection = 'column'

    loadingDiv.insertAdjacentHTML('afterbegin', `<span>${v}</span>`)
    loadingDiv.insertAdjacentHTML(
      'beforeend',
      `<div style="width:100%; margin: 5px 0px; height: 0; border-bottom: 1px dashed #000"></div>
      <div class="__translateloading">
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot"></div>
      </div>`
    )
    return loadingDiv
  }

  async translateMessage(element, options = {}) {
    if (!this.config.self) {
      return
    }

    if (element.hasAttribute('data-translated')) {
      if (
        element.getAttribute('data-translated') === this.config.recive_lang ||
        element.getAttribute('data-translated') === 'tranlating'
      ) {
        return true
      }
    } else {
      element.setAttribute('data-originaltext', element.innerText)
    }

    element.setAttribute('data-translated', 'tranlating')
    element.replaceChildren(this.createLoadingDiv(element.innerText))
    let v = element.getAttribute('data-originaltext')
    let params = {
      text: v,
      engineCode: this.config.engineCode,
      fromLang: 'auto',
      toLang: this.config.recive_lang,
      time: 0, // 接收时间
      type: options.type || 'out', // 接收a
      ccountId: this.dependPlatform.userId,
      fansId: options.fansId || this.dependPlatform.chatUserId,
      platform: this.dependPlatform.platform
    }
    const tv = await this._translate(params)
    // 待插入的html
    var html = document.createElement('div')
    html.insertAdjacentHTML('beforeend', `<span>${v}</span>`)
    if (tv) {
      html.insertAdjacentHTML(
        'beforeend',
        `<div style="width:100%; margin: 5px 0px; height: 0; border-bottom: 1px dashed currentColor"></div>`
      )
      html.insertAdjacentHTML('beforeend', `<span>${tv}</span>`)
    }

    element.replaceChildren(html)
    element.setAttribute('data-translated', this.config.recive_lang ? this.config.recive_lang : '')
    if (options.callback) options.callback()
  }

  formatResponse(res) {
    let tv = ''
    if (res.hasOwnProperty('code') && res.code === 1) {
      tv = res.data.result
    } else if (res.value) {
      tv = res.value
    }
    return tv
  }
}

class TranslateMessageInfo {
  constructor({ engineCode, fromLang, toLang, text, translatedText }) {
    this.engineCode = engineCode
    this.fromLang = fromLang
    this.toLang = toLang
    this.text = text
    this.translatedText = translatedText
  }
}
