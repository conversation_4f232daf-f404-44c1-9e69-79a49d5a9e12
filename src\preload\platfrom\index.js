import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { Translater } from '../tanslate/index'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
dayjs.extend(isBetween)

/**
 * 判断当前时间是否在任意时间段内（仅比较当天的时分秒）
 * @param ranges 时间段数组，例如 [["00:00:00", "00:48:02"], ["12:00:00", "13:30:00"]]
 * @returns 是否在任意时间段内
 */
export function isNowInRange(ranges) {
  const now = dayjs()
  const today = dayjs().format('YYYY-MM-DD')

  return ranges.some(([startStr, endStr]) => {
    const start = dayjs(`${today} ${startStr}`)
    const end = dayjs(`${today} ${endStr}`)
    return now.isBetween(start, end, null, '[)')
  })
}
export class Platform {
  constructor(platform) {
    this.platform = platform
  }
  platform = null
  mutationObserver = null
  pathObserver = null
  viewSessionId
  sI = null
  isAutoAiReplying = false

  aiReplyConfig = null

  async updateAiReplyConfig() {

    ipcRenderer.on('updateAiReplyConfig', (event, arg) => {
      this.aiReplyConfig = arg
    })
    this.aiReplyConfig = await ipcRenderer.invoke('getAiReplyConfig')
  }

  forgeFingerprints() {
    spoofNavigator();
    spoofCanvas();

    console.log(navigator);

    // =============== Navigator 伪装 ===============
    function spoofNavigator() {
      const overwrite = (obj, prop, value) => {
        Object.defineProperty(obj, prop, {
          get: () => value,
          configurable: true,
        });
      };
      overwrite(navigator, 'userAgentData', {
        brands: [
          { brand: "Chromium", version: "137" },
          { brand: "Not/A)Brand", version: "8" }
        ],
        mobile: false,
        platform: "Windows",
        getHighEntropyValues: (hints) => {
          return Promise.resolve({
            platform: "Windows",
            platformVersion: "10.0.0",
            architecture: "x86",
            model: "",
            uaFullVersion: "*********"
          });
        }
      });
      overwrite(navigator, 'language', 'en-US');
      overwrite(navigator, 'languages', ['en-US', 'en']);
      overwrite(navigator, 'hardwareConcurrency', 4);
      overwrite(navigator, 'deviceMemory', 8);
    }

    // =============== Canvas 伪装 ===============
    function spoofCanvas() {
      const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
      HTMLCanvasElement.prototype.toDataURL = function (...args) {
        try {
          const ctx = this.getContext('2d');
          injectCanvasNoise(ctx, this.width, this.height);
        } catch (e) { }
        return originalToDataURL.apply(this, args);
      };

      const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
      CanvasRenderingContext2D.prototype.getImageData = function (...args) {
        injectCanvasNoise(this, this.canvas.width, this.canvas.height);
        return originalGetImageData.apply(this, args);
      };

      function injectCanvasNoise(ctx, width, height) {
        if (!ctx || typeof ctx.fillText !== 'function') return;
        ctx.save();
        ctx.globalAlpha = 0.01;
        ctx.fillStyle = '#000';
        ctx.font = '16px Arial';
        ctx.fillText(`canvas-noise-${Math.random() * 10}`, 1, height - 1);
        ctx.restore();
      }
    }

    // // =============== WebGL 伪装 ===============
    // function spoofWebGL() {
    //   const getParameterProxy = WebGLRenderingContext.prototype.getParameter;
    //   WebGLRenderingContext.prototype.getParameter = function (param) {
    //     const spoofedParams = {
    //       37445: 'Fake GPU Corp.',         // UNMASKED_VENDOR_WEBGL
    //       37446: 'Fake GPU Model 9000',    // UNMASKED_RENDERER_WEBGL
    //       33901: 4096,                     // MAX_TEXTURE_SIZE
    //       3379: 16                         // MAX_TEXTURE_IMAGE_UNITS
    //     };
    //     if (param in spoofedParams) return spoofedParams[param];
    //     return getParameterProxy.call(this, param);
    //   };

    //   const getExtensionProxy = WebGLRenderingContext.prototype.getExtension;
    //   WebGLRenderingContext.prototype.getExtension = function (name) {
    //     if (name === 'WEBGL_debug_renderer_info') return null;
    //     return getExtensionProxy.call(this, name);
    //   };
    // }

    // // =============== 屏幕信息伪装 ===============
    // function spoofScreen() {
    //   const overwrite = (obj, prop, value) => {
    //     Object.defineProperty(obj, prop, {
    //       get: () => value,
    //       configurable: true,
    //     });
    //   };

    //   overwrite(window.screen, 'width', 1920);
    //   overwrite(window.screen, 'height', 1080);
    //   overwrite(window.screen, 'availWidth', 1920);
    //   overwrite(window.screen, 'availHeight', 1040);
    //   overwrite(window.screen, 'colorDepth', 24);
    //   overwrite(window.screen, 'pixelDepth', 24);
    // }

    // // =============== Plugins 伪装 ===============
    // function spoofPlugins() {
    //   const fakePlugin = {
    //     name: 'Chrome PDF Plugin',
    //     filename: 'internal-pdf-viewer',
    //     description: 'Portable Document Format',
    //   };

    //   Object.defineProperty(navigator, 'plugins', {
    //     get: () => [fakePlugin],
    //     configurable: true,
    //   });

    //   Object.defineProperty(navigator, 'mimeTypes', {
    //     get: () => [{
    //       type: 'application/pdf',
    //       suffixes: 'pdf',
    //       description: '',
    //       enabledPlugin: fakePlugin,
    //     }],
    //     configurable: true,
    //   });
    // }
  }

  initContextMenu() {
    let menuElement = document.createElement("div");
    let copy_text = ""

    const menuItems = [{
      class: "menu-item",
      id: "copy-option",
      textContent: "📋 复制",
      tag: "div",
      onClick: async (e) => {
        e.preventDefault()
        if (copy_text) {
          try {
            await navigator.clipboard.writeText(copy_text);
            e.target.innerText = '📋 已复制'
            setTimeout(() => {
              menuElement.style.display = 'none'
              e.target.innerText = '📋 复制'
              window.getSelection().removeAllRanges()
            }, 500)
          } catch (err) {
            console.error("❌ 复制失败: " + err.message);
          }
        }

      }
    }]
    const menuElList = []

    menuItems.forEach(item => {
      const el = document.createElement(item.tag)
      el.id = item.id
      el.className = item.class
      el.textContent = item.textContent
      el.onclick = item.onClick
      menuElList.push(el)
    })
    menuElement.id = "custom-menu"
    menuElement.append(...menuElList)
    document.body.append(menuElement)

    document.addEventListener('mouseup', (event) => {
      const selection = window.getSelection()
      if (!selection && !selection.focusNode) return
      const input = document.querySelector(this.sI.input || this.sI.inputElSelector)
      if (input && input.contains(selection.focusNode)) {
        return
      }
      setTimeout(() => {
        const selectedText = selection.toString().trim()
        if (selectedText) {
          const info = {
            x: event.clientX,
            y: event.clientY,
          };

          const x = info.x + 110 > document.body.clientWidth ? info.x - 110 : info.x
          const y = info.y + 110 > document.body.clientWidth ? info.y - 110 : info.y
          menuElement.style.top = `${y}px`;
          menuElement.style.left = `${x}px`;
          menuElement.style.display = "block";
          copy_text = selectedText
          console.log("右键点击信息:", copy_text, info, event);
        }
      }, 0)
    })
    let isClickingMenu = false

    // 标记点击菜单
    menuElement.addEventListener('mousedown', () => {
      isClickingMenu = true
    })


    document.addEventListener('mousedown', (e) => {
      if (!menuElement.contains(e.target)) {
        isClickingMenu = false
      }
    })

    document.addEventListener('click', (e) => {
      if (!isClickingMenu && !menuElement.contains(e.target)) {
        menuElement.style.display = 'none'
      }
    })
  }

  async getSelectorInfo() {
    this.sI = await ipcRenderer.invoke('getSelectorInfo', {
      platform: this.platform
    })
    console.log('SelectorInfo: %O\n', this.sI)
  }

  async init(translater) {

    if (!this.platform) {
      console.warn('platform is null')
      alert('无法识别的平台，请联系管理员')
      return
    }
    document.addEventListener('DOMContentLoaded', async () => {
      await this.initViewFunc()
      this.initObserver()
      this.initContextMenu()
      this.initEventHandler()
    })
    this.updateAiReplyConfig()
    await this.getViewSessionId()
    await this.getSelectorInfo()

    if (translater instanceof Translater) {
      this.translater = translater
      this.translater.init(this)
    }
  }

  async getViewSessionId() {
    this.viewSessionId = await ipcRenderer.invoke('getViewSessionId')
    console.log('viewSessionId: %s\n', this.viewSessionId)
  }

  initObserver() {
    // throttle
    this.mutationObserver = new MutationObserver(
      throttle(this._o.bind(this), 100, { trailing: true })
    )
    this.mutationObserver.observe(document.body, { subtree: true, childList: true })
  }
  _o(mutations, observer) {

  }
  _u() { }

  sendMessageToInput(...args) {
    console.log('sendMessageToInput', ...args)
  }
  autoReplyHandler() {
    console.log('autoReplyHandler')
  }

  async clickAt(params) {
    return await ipcRenderer.invoke("clickAt", params)
  }
  async aiReply(callback) {
    // && isNowInRange([this.aiReplyConfig.aiTimeRang])
    try {
      return new Promise(async (resovle) => {
        if (!this.isAutoAiReplying && this.aiReplyConfig.ai) {
          this.isAutoAiReplying = true;
          await callback()
          console.log("aiReply")
          this.isAutoAiReplying = false;
          resovle()
        }
      })
    } catch (error) {
      this.isAutoAiReplying = false;
      console.log(error);
    }
  }

  translateList() { }
  async getAiReply(params) {
    try {
      let v = await ipcRenderer.invoke('aiAutoReply', params)
      return v
    } catch (error) {
      return
    }

  }

  onUserLoginOut() {
    ipcRenderer.on('onUserLoginOut ', () => {
      localStorage.clear()
      sessionStorage.clear()
      cookieStore.getAll().then((res) => {
        res.forEach((key) => {
          cookieStore.delete(key)
        })
      })
    })
  }

  async initViewFunc() {
    this.onUserLoginOut()
    let r = await ipcRenderer.invoke('initViewFunc')
    if (r) {
      // 监听hash
      window.addEventListener('hashchange', () => {
        this._u(window.location)
      })
      // 监听 popstate 事件
      window.addEventListener('popstate', () => {
        this._u(window.location)
      })

      // 监听 pushstate 事件
      window.addEventListener('pushstate', () => {
        this._u(window.location)
        console.log('使用 pushState 改变了 URL:', window.location.href)
      })

      // 监听 replacestate 事件
      window.addEventListener('replacestate', () => {
        this._u(window.location)
        console.log('使用 replaceState 改变了 URL:', window.location.href)
      })
      return true
    } else {
      return false
    }
  }

  sendPlatformUserInfo(params) {
    //   nickName,
    //   phone,
    //   userId,
    //   platform,
    //   session_id,
    ipcRenderer.send('sendUserInfoViewToMain', params)
  }

  _sendUnReadCount(unReadCount) {
    ipcRenderer.send('setUnReadCount', { platform: this.platform, unReadCount })
  }

  sendNewFansList(params) {
    ipcRenderer.send('addNewFansList', params)
  }

  sendUnReadCount = throttle(this._sendUnReadCount, 2000, { trailing: true })

  sendCurrentSessionFansInfo(params) {
    ipcRenderer.send('sendActiveFansIdViewToMain', params)
  }

  initEventHandler() {
    ipcRenderer.on('onMessageMainToView', (_event, ...args) => {
      this.sendMessageToInput(...args)
    })
  }
}

export const throttle = (func, delay, options = { trailing: true }) => {
  let lastTime = 0
  let timer = null
  let lastArgs = null
  let lastThis = null

  return function (...args) {
    const now = Date.now()
    const remainingTime = delay - (now - lastTime)

    // 如果剩余时间 <= 0，或者从未执行过，则立即执行
    if (remainingTime <= 0 || lastTime === 0) {
      // 清除之前的尾部调用定时器
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
      lastTime = now
      func.apply(this, args)
    }
    // 如果允许尾部调用，并且没有定时器在等待
    else if (options.trailing && !timer) {
      lastArgs = args
      lastThis = this
      // 设置定时器，在剩余时间后执行尾部调用
      timer = setTimeout(() => {
        lastTime = Date.now()
        timer = null
        func.apply(lastThis, lastArgs)
      }, remainingTime)
    }
  }
}

const hostNameMap = new Map([
  ['www.facebook.com', 'facebook'],
  ['whatsapp', 'whatsapp'],
  ['telegram', 'telegram'],
  ['instagram', 'instagram'],
  ['tiktok', 'tiktok'],
  ['business.facebook.com', 'facebookBusiness'],
  ['x.com', 'twitter'],
  ['discord.com', 'discord']
])

export const getPlatform = () => {
  let host = JSON.parse(import.meta.env.VITE_PLATFROM_HOST_NAMES).find((name) => {
    if (window.location.host.match(name)) {
      return true
    } else {
      return false
    }
  })

  return hostNameMap.get(host)
}

export * from '../tanslate/index'
export * from './facebook'
export * from './instagram'
export * from './whatsapp'
export * from './telegram'
export * from './tiktok'
export * from './facebookBusiness'
export * from './twitter'
export * from './discord'
