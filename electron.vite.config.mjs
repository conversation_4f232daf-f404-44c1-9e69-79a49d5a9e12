import { resolve } from 'path'
import UnoCSS from 'unocss/vite'
import { defineConfig, externalizeDepsPlugin, loadEnv, bytecodePlugin } from 'electron-vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd())
  const config = {
    main: {
      base: './',
      resolve: {
        alias: {
          '@main': resolve('./src/main')
        }
      },
      esbuild: {
        drop: ['debugger']
      },
      plugins: [externalizeDepsPlugin()]
    },
    preload: {
      plugins: [externalizeDepsPlugin()],
      base: './',
      build: {
        rollupOptions: {
          output: {
            preserveModules: true,
            preserveModulesRoot: 'src/preload'
          }
        }
      }
    },
    renderer: {
      resolve: {
        alias: {
          '@renderer': resolve(__dirname, './src/renderer/src')
        }
      },
      esbuild: {
        drop: ['console', 'debugger']
      },
      plugins: [
        UnoCSS(),
        vue({
          template: {
            compilerOptions: {
              isCustomElement: (tag) => ['webview'].includes(tag)
            }
          }
        })
      ],
      server: {
        proxy: {
          '/apis': {
            target: env.VITE_BASE_API, // 后端服务地址
            changeOrigin: true,
            rewrite: (path) => {
              return path.replace(/^\/apis/, '')
            }
          }
        }
      }
    }
  }
  if (command === 'build') {
    const listBytecode = ['main']
    // listBytecode.forEach((key) => {
    //   // 添加加固代码插件
    //   config[key].plugins.push(bytecodePlugin())
    // })

    const listMinify = ['renderer', 'preload']
    listMinify.forEach((key) => {
      if (config[key].build) {
        config[key].build.minify = true
      } else {
        config[key].build = { minify: true }
      }
    })
  }
  return config
})
