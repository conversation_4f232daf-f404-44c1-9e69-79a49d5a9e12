import 'virtual:uno.css'
import '@unocss/reset/normalize.css'
import '@unocss/reset/tailwind-compat.css'
import './assets/style/main.css'

import { createApp } from 'vue'
import App from './App.vue'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
// 持久化插件
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
import router from './router'
import i18n from './utils/i118'
import { zhCn } from 'element-plus/es/locale/index.mjs'
const app = createApp(App)
app.use(pinia)
app.use(i18n)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn
})
app.mount('#app')
electron.ipcRenderer.invoke('getPlatform').then((res) => {
  window.platform = res
})
