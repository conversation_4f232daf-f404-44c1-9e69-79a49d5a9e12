import { contextBridge } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import {
  getPlatform,
  FacebookHandler,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Telegram<PERSON><PERSON><PERSON>,
  <PERSON>late<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FacebookBusiness<PERSON><PERSON>ler,
  Twitter<PERSON><PERSON>ler,
  <PERSON>rdHandler
} from './platfrom'
try {
  let platformHandler = null
  let init = () => {
    // Use `contextBridge` APIs to expose Electron APIs to
    // renderer only if context isolation is enabled, otherwise
    // just add to the DOM global.
    if (process.contextIsolated) {
      contextBridge.exposeInMainWorld('electron', {
        ...electronAPI,
        ipcRenderer: {
          ...electronAPI.ipcRenderer,
          send: (...args) => {
            electronAPI.ipcRenderer.send(...args)
          },
          invoke: (...args) => {
            return electronAPI.ipcRenderer.invoke(...args)
          }
        }
      })
    } else {
      window.electron = electronAPI
    }

    /** */
    const platform = getPlatform()
    if (platform) {
      switch (platform) {
        case 'facebook':
          platformHandler = new FacebookHandler(platform)
          break
        case 'whatsapp':
          platformHandler = new WhatsappHandler(platform)
          break
        case 'telegram':
          platformHandler = new TelegramHandler(platform)
          break
        case 'instagram':
          platformHandler = new InstagramHandler(platform)
          break
        case 'tiktok':
          platformHandler = new TiktokHandler(platform)
          break
        case 'facebookBusiness':
          platformHandler = new FacebookBusinessHandler(platform)
          break
        case 'twitter':
          platformHandler = new TwitterHandler(platform)
          break
        case 'discord':
          platformHandler = new DiscordHandler(platform)
          break
      }
      if (platformHandler) {
        platformHandler.init(new Translater())
      }
    }
  }

  init()
} catch (error) {
  console.error(error)
}
